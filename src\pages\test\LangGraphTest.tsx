import { useStream } from "@langchain/langgraph-sdk/react";
import UnifiedHeader from "@/components/toolbars/UnifiedHeader";
import { useState, useRef, useEffect } from "react";
import { Send, Loader2, Bug } from "lucide-react";
import ReactMarkdown from "react-markdown";
import { Message } from "@langchain/langgraph-sdk";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

import { useAuth } from "@/hooks/auth/useAuth";
import { useLocation } from "@/contexts/LocationContext";
import { ToolMessage } from "@langchain/core/messages";

/**
 * LangGraph Chat Page
 *
 * A modern chat interface for testing LangGraph integration
 */

const formatMessageContent = (
  content: string | Array<{ type: string; text?: string }>
) => {
  if (typeof content === "string") {
    // Try to parse as JSON to extract llmResponse
    try {
      const parsed = JSON.parse(content);
      if (parsed.llmResponse) {
        return parsed.llmResponse;
      }
      return content;
    } catch {
      // If not valid JSON, return as is
      return content;
    }
  }
  if (Array.isArray(content)) {
    return content
      .map((part) => {
        if (part.type === "text") {
          // Try to parse text as JSON to extract llmResponse
          try {
            const parsed = JSON.parse(part.text || "");
            if (parsed.llmResponse) {
              return parsed.llmResponse;
            }
            return part.text;
          } catch {
            return part.text;
          }
        }
        return null;
      })
      .join("");
  }
  return "";
};

const HumanMessage = ({ content }: { content: string }) => {
  return (
    <div className="whitespace-pre-wrap break-words">
      {formatMessageContent(content)}
    </div>
  );
};
const AIMessage = ({ content }: { content: string }) => {
  return (
    <div className="prose max-w-none prose-p:break-words">
      <ReactMarkdown>{formatMessageContent(content)}</ReactMarkdown>
    </div>
  );
};
const ToolMessage = ({ tool }: { tool: ToolMessage }) => {
  return (
    <div className="flex justify-start">
      <div className="bg-muted text-foreground shadow-sm border border-border px-4 py-2 rounded-lg flex items-center space-x-2">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm">{tool.tool_call_id}</span>
         <span className="text-sm">{tool.artifact}</span>
      </div>
    </div>
  );
};
const LoadingResponse = () => {
  return (
    <div className="flex justify-start">
      <div className="bg-card text-foreground shadow-sm border border-border px-4 py-2 rounded-lg flex items-center space-x-2">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm">Thinking...</span>
      </div>
    </div>
  );
};

const DebugMessage = ({
  timestamp,
  tool,
  event,
}: {
  timestamp: string;
  tool: string;
  event: unknown;
}) => {
  return (
    <div className="flex justify-start">
      <div className="bg-muted text-muted-foreground shadow-sm border border-border px-4 py-2 rounded-lg max-w-[280px] sm:max-w-sm lg:max-w-md">
        <div className="flex items-center space-x-2 mb-2">
          <Bug className="h-4 w-4" />
          <span className="text-xs font-medium">DEBUG</span>
          <span className="text-xs text-muted-foreground">
            {new Date(timestamp).toLocaleTimeString()}
          </span>
        </div>
        <div className="text-sm">
          <div className="font-medium mb-1">Tool: {tool}</div>
          <div className="text-xs bg-muted p-2 rounded border overflow-auto max-h-32">
            <pre className="whitespace-pre-wrap">
              {JSON.stringify(event, null, 2)}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
};

const LangGraphTest = () => {
  const [inputValue, setInputValue] = useState("");
  const [showToolCallWheel, setShowToolCallWheel] = useState(false);
  const [currentTool, setCurrentTool] = useState("");
  const [debugMode, setDebugMode] = useState(true);
  const [debugMessages, setDebugMessages] = useState<
    Array<{
      timestamp: string;
      tool: string;
      event: unknown;
      messageIndex: number;
    }>
  >([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const { user } = useAuth();
  const locationState = useLocation();
  const latitude = locationState?.coordinates?.lat;
  const longitude = locationState?.coordinates?.lng;
  
  const [threadId, setThreadId] = useState<string | null>(null);
  
//const thread = useStream<{ messages: Message[] }>({  // this should fit the message model used by   thread.submit
  const thread = useStream({
    apiUrl: "https://lancatchup.onrender.com",
    //apiUrl: "http://localhost:8000",
    assistantId: "catchup",
    messagesKey: "messages",

    threadId: threadId,
    onThreadId: setThreadId,  
    
    onCustomEvent: (event, options) => {
      console.log("Custom event:", event, options);
      // Handle custom tool call events
      if (event && typeof event === "object" && "custom_tool_call" in event) {
        setShowToolCallWheel(true);
        setCurrentTool(event.custom_tool_call as string);

        // Store debug message if debug mode is active
        if (debugMode) {
          setDebugMessages((prev) => [
            ...prev,
            {
              timestamp: new Date().toISOString(),
              tool: event.custom_tool_call as string,
              event: event,
              messageIndex: thread.messages.length, // Track which message this debug event relates to
            },
          ]);
        }
      }

      // Handle custom events like progress updates
      // if (event && typeof event === 'object' && 'payload' in event) {
      //   options.mutate((prev) => ({
      //     ...prev,
      //     progress: event.payload,
      //   }));
      // }
    },

    onFinish: (message) => {
      console.log("Stream finished:", message);
      setShowToolCallWheel(false); // Hide wheel when message arrives
    },
    onError: (error) => {
      console.error("useStream Error:", error);
      setShowToolCallWheel(false);
    },
    onMetadataEvent(data) {
      console.log("Metadata event:", data);
    },
    onUpdateEvent: (data) => {
      console.log("Update event - new message data:", data);
      // Handle real-time message updates here
    },
    // onCreated: (run) => {
    //   window.sessionStorage.setItem(`resume:${run.thread_id}`, run.run_id);
    // },
    //reconnectOnMount: () => window.localStorage,
  });

  // Hide wheel when new messages arrive
  useEffect(() => {
    if (thread.messages.length > 0) {
      setShowToolCallWheel(false);
    }
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [thread.messages]);

  // Check if we're currently streaming a response (LLM is generating content)
  const isStreamingResponse = () => {
    if (!thread.isLoading) return false;

    // If we have messages and the last message is from AI and loading is true,
    // it means we're likely streaming that AI response
    const lastMessage = thread.messages[thread.messages.length - 1];
    return lastMessage && lastMessage.type === "ai";
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim() || thread.isLoading) return;

    thread.submit({
      messages: [{ type: "human", content: inputValue.trim() }],
      user_id: user?.id,
      email_address: user?.email,
      latitude: latitude !== undefined ? String(latitude) : undefined,
      longitude: longitude !== undefined ? String(longitude) : undefined,
      session_id: "12",
      memory_lenght: "15",
    });
    // thread.submit({
    //   messages: [{ type: "human", content: inputValue.trim() }],
    //   user_context: {
    //     user_id: "fe95e629-0a4e-474b-97d1-fafe9d6863e3",
    //     email_address: "<EMAIL>",
    //     location: {
    //       latitude: 45.4666,
    //       longitude: 9.1832
    //     },
    //     current_session_start: new Date().toISOString()
    //   },
    //   session_config: {
    //     session_id: "1233",
    //     memory_budget: 15,
    //     max_tool_calls_per_turn: 3,
    //     conversation_timeout: 30,
    //     preferred_response_style: "conversational",
    //     enable_proactive_suggestions: true
    //   },
    //   current_phase: "greeting",
    //   conversation_metrics: {
    //     message_count: 0,
    //     tool_calls_count: 0,
    //     successful_tool_calls: 0,
    //     failed_tool_calls: 0,
    //     conversation_start: new Date().toISOString(),
    //     last_activity: new Date().toISOString(),
    //     estimated_tokens_used: 0,
    //     user_satisfaction_indicators: []
    //   },
    //   plan: ""
    // });
    setInputValue("");
  };

  const handleSuggestionClick = (text: string) => {
    if (thread.isLoading) return;
    thread.submit({
      messages: [{ type: "human", content: text }],
      user_id: user?.id,
      email_address: user?.email,
      latitude: latitude !== undefined ? String(latitude) : undefined,
      longitude: longitude !== undefined ? String(longitude) : undefined,
      session_id: "12",
      memory_lenght: "15",
    });
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <div className="fixed top-0 left-0 right-0 z-10">
        <UnifiedHeader
          title="AI Assistant"
          isBusiness={false}
          showBackButton={true}
        />
      </div>

      {/* Debug Switch */}
      <div className="fixed top-16 left-0 right-0 z-20 bg-card border-b border-border px-4 py-2">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Switch
              id="debug-mode"
              checked={debugMode}
              onCheckedChange={setDebugMode}
            />
            <Label htmlFor="debug-mode" className="text-sm font-medium">
              Show tool call
            </Label>
          </div>
          {debugMode && (
            <button
              onClick={() => setDebugMessages([])}
              className="text-xs text-gray-500 hover:text-gray-700"
            >
              Clear Debug
            </button>
          )}
        </div>
      </div>

      {/* Chat Messages Area */}
      <main className="flex-1 pt-28 pb-24 overflow-hidden">
        <div className="h-full flex flex-col max-w-4xl mx-auto">
          <div className="flex-1 overflow-y-auto px-4 py-6 space-y-4">
            {thread.messages.length === 0 ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center text-muted-foreground">
                  <h1 className="sr-only">Assistente CatchUp - Chat</h1>
                  <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-muted flex items-center justify-center text-sm font-semibold">
                    A
                  </div>
                  <p className="text-base font-medium">
                    Come posso aiutarti oggi?
                  </p>
                </div>
              </div>
            ) : null}
            {(() => {
              const renderedItems: JSX.Element[] = [];

              // Render messages and interleave debug messages
              thread.messages.forEach((message, index) => {
                if (
                  message.type === "human" ||
                  message.type === "ai" ||
                  message.type === "tool"
                ) {
                  // Render debug messages that occurred before this message
                  if (debugMode) {
                    debugMessages
                      .filter((debugMsg) => debugMsg.messageIndex === index)
                      .forEach((debugMsg, debugIndex) => {
                        renderedItems.push(
                          <DebugMessage
                            key={`debug-${index}-${debugIndex}`}
                            timestamp={debugMsg.timestamp}
                            tool={debugMsg.tool}
                            event={debugMsg.event}
                          />
                        );
                      });
                  }

                  // Render the actual message
                  renderedItems.push(
                    <div
                      key={message.id || `msg-${index}`}
                      className={`flex ${
                        message.type === "human"
                          ? "justify-end"
                          : "justify-start"
                      }`}
                    >
                      <div
                        className={`max-w-[280px] sm:max-w-sm lg:max-w-md px-4 py-2 rounded-lg ${
                          message.type === "human"
                            ? "bg-primary text-primary-foreground"
                            : "bg-card text-foreground shadow-sm border border-border"
                        }`}
                      >
                        {message.type === "human" ? (
                          <HumanMessage content={message.content as string} />
                        ) : message.type === "tool" ? (
                          <ToolMessage tool={message as unknown as ToolMessage} />
                        ) : message.type === "ai" && message.content ? (
                          <AIMessage content={message.content as string} />
                        ) : (
                           <ToolMessage tool={message as unknown as ToolMessage} ></ToolMessage>
                         
                        )}
                      </div>
                    </div>
                  );
                }
              });

              // Add any remaining debug messages that occurred after the last message
              if (debugMode) {
                debugMessages
                  .filter(
                    (debugMsg) =>
                      debugMsg.messageIndex >= thread.messages.length
                  )
                  .forEach((debugMsg, debugIndex) => {
                    renderedItems.push(
                      <DebugMessage
                        key={`debug-end-${debugIndex}`}
                        timestamp={debugMsg.timestamp}
                        tool={debugMsg.tool}
                        event={debugMsg.event}
                      />
                    );
                  });
              }

              return renderedItems;
            })()}

            {thread.interrupt && (
              <div className="text-sm text-muted-foreground">
                Interrotto!
                <button
                  type="button"
                  onClick={() => {
                    thread.submit(undefined, { command: { resume: true } });
                  }}
                  className="ml-2 underline"
                >
                  Riprendi
                </button>
                {thread.interrupt ? (
                  <div className="mt-1 text-xs break-words">
                    {String((thread.interrupt as any)?.value?.message ?? "")}
                  </div>
                ) : null}
              </div>
            )}
            {/* Tool call loading indicator */}
            {showToolCallWheel ? <ToolMessage tool={currentTool} /> : null}
            {/* Loading indicator - only show when not streaming a response */}
            {thread.isLoading &&
            !showToolCallWheel &&
            !isStreamingResponse() ? (
              <LoadingResponse />
            ) : null}
            <div ref={messagesEndRef} />
          </div>
        </div>
      </main>

      {/* Input & Suggestions Area */}
      <div className="fixed bottom-0 left-0 right-0 bg-card border-t border-border px-4 py-3 z-20">
        <div className="max-w-4xl mx-auto space-y-2">
          {/* Suggested prompts */}
          <div className="flex gap-2 overflow-x-auto hide-scrollbar">
            {[
              "Cerco un taglio di capelli vicino a me, dopo un aperitivo e dopo una spa. Dalle 15.00 nei prossimi giorni.",
              "Mi mandi le mie prenotazioni su whatsapp",
              "Cerco un ristorante per questa sera.",
            ].map((s, i) => (
              <button
                key={i}
                onClick={() => handleSuggestionClick(s)}
                className="px-3 py-2 rounded-full border border-border bg-card hover:bg-muted text-sm whitespace-nowrap"
                type="button"
              >
                {s}
              </button>
            ))}
          </div>

          {/* Input */}
          <form onSubmit={handleSubmit} className="flex gap-2 items-center">
            <input
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="Scrivi un messaggio..."
              className="flex-1 px-4 py-3 rounded-lg border border-input bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              disabled={thread.isLoading}
            />
            <button
              type="submit"
              disabled={!inputValue.trim() || thread.isLoading}
              className="px-4 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              aria-label="Invia"
            >
              {thread.isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </button>
          </form>
        </div>
      </div>

      {/* <div className="fixed bottom-0 left-0 right-0">
        <BottomNavigationBar isBusiness={false} />
      </div> */}
    </div>
  );
};

export default LangGraphTest;
