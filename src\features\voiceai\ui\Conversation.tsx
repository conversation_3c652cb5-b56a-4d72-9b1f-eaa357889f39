import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";


export interface ChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: string;
}
interface ConversationProps {
  messages: ChatMessage[];
  agentImageUrl: string;
}

const Conversation = ( { messages, agentImageUrl }: ConversationProps) => {
  return (

 <div id="conversation-area" className="space-y-4 mb-6 h-96 overflow-y-auto">
      {messages.map((message) => {
        const isUser = !!message.isUser;
        return (
          <div key={message.id} id={message.id} className={`flex items-start space-x-3 ${isUser ? "flex-row-reverse" : ""}`}>
            <div className={`w-8 h-8 ${isUser ? "bg-primary/10" : "bg-secondary/10"} rounded-full flex items-center justify-center flex-shrink-0`}>
              <Avatar className="w-6 h-6">
                <AvatarImage src={agentImageUrl} alt="English" />
                <AvatarFallback>IA</AvatarFallback>
              </Avatar>
            </div>
            <div className="flex-1">
              <div className={`${isUser
                ? "bg-card text-card-foreground border border-border"
                : "bg-primary text-primary-foreground"
              } rounded-2xl p-4 shadow-md`}>
                <p className={`text-base leading-relaxed ${isUser ? "text-card-foreground" : "text-primary-foreground"} font-medium`}>
                  {message.content}
                </p>
                <div className="flex items-center space-x-2">
                  <button
                    className={`inline-flex items-center justify-center rounded-md transition-colors
                      focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2
                      focus-visible:ring-primary focus-visible:ring-offset-background
                      ${isUser ? "text-muted-foreground hover:text-foreground"
                               : "text-primary-foreground/90 hover:text-primary-foreground"}`}
                    aria-label="Ascolta messaggio"
                  >
                    <i className="fas fa-volume-up text-sm"></i>
                  </button>
                  <span className={`text-xs ${isUser ? "text-muted-foreground" : "text-primary-foreground/80"}`}>
                    {message.timestamp}
                  </span>
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>

  )
};

export default Conversation;
