
import React from 'react';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';
import { Bell, Users, Calendar, MessageCircle, CheckCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface EnhancedNotificationCardProps {
  notification: {
    id: string;
    entity: 'group_invite' | 'booking' | 'messages' | 'others';
    entity_id: string;
    created_at: string;
  };
  onClick?: () => void;
  variant?: 'default' | 'success' | 'booking';
}

const EnhancedNotificationCard = ({ notification, onClick, variant = 'default' }: EnhancedNotificationCardProps) => {


  const getNotificationDetails = (entity: string, variant: string) => {
    if (variant === 'booking') {
      return {
        icon: <Calendar className="h-5 w-5 text-blue-600" />,
        title: "Prenotazione Confermata",
        message: "La tua prenotazione è stata confermata! Tocca per aprire la chat",
        bgColor: "bg-blue-50",
        borderColor: "border-blue-200", 
        badgeColor: "bg-blue-500 text-white"
      };
    }

    switch (entity) {
      case 'group_invite':
        return {
          icon: <Users className="h-5 w-5 text-purple-500" />,
          title: "Invito al Gruppo",
          message: "Hai ricevuto un nuovo invito per unirti a un gruppo",
          bgColor: "bg-purple-50",
          borderColor: "border-purple-200",
          badgeColor: "bg-purple-500 text-white"
        };
      case 'booking':
        return {
          icon: <Calendar className="h-5 w-5 text-green-500" />,
          title: "Prenotazione",
          message: "Aggiornamento sulla tua prenotazione",
          bgColor: "bg-green-50",
          borderColor: "border-green-200",
          badgeColor: "bg-green-500 text-white"
        };
      case 'message':
        return {
          icon: <MessageCircle className="h-5 w-5 text-orange-500" />,
          title: "Nuovo Messaggio",
          message: "Hai ricevuto un nuovo messaggio",
          bgColor: "bg-orange-50",
          borderColor: "border-orange-200",
          badgeColor: "bg-orange-500 text-white"
        };
      default:
        return {
          icon: <Bell className="h-5 w-5 text-gray-500" />,
          title: "Notifica",
          message: "Hai una nuova notifica",
          bgColor: "bg-gray-50",
          borderColor: "border-gray-200",
          badgeColor: "bg-gray-500 text-white"
        };
    }
  };

  const details = getNotificationDetails(notification.entity, variant);

  return (
    <div
      onClick={onClick}
      className={`${details.bgColor} ${details.borderColor} border rounded-lg p-4 cursor-pointer hover:shadow-md transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]`}
    >
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0 mt-1">
          {details.icon}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <h3 className="font-medium text-gray-900 text-sm">
              {details.title}
            </h3>
            <span className="text-xs text-gray-500 flex-shrink-0">
              {format(new Date(notification.created_at), "HH:mm", { locale: it })}
            </span>
          </div>
          
          <p className="text-sm text-gray-600 mb-2">
            {details.message}
          </p>
          
          <div className="flex items-center justify-between">
            <Badge className={`text-xs ${details.badgeColor}`}>
              {variant === 'booking' ? 'Prenotazione' : notification.entity.replace('_', ' ')}
            </Badge>
            
            <span className="text-xs text-gray-400">
              {format(new Date(notification.created_at), "dd MMM", { locale: it })}
            </span>
          </div>

          {variant === 'booking' && (
            <div className="flex items-center gap-1 mt-2">
              <CheckCircle className="h-3 w-3 text-blue-500" />
              <span className="text-xs text-blue-600 font-medium">
                Tocca per chattare
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EnhancedNotificationCard;
