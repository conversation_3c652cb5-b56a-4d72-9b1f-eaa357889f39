import React from "react";
import { usePWAUpdate } from "@/hooks/pwa/usePWAUpdate";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { RefreshCw, X } from "lucide-react";

export const PWAUpdateNotification: React.FC = () => {
  const { updateAvailable, isUpdating, updateAndReload, dismissUpdate } =
    usePWAUpdate();

  if (!updateAvailable) {
    return null;
  }

  return (
    <Alert className="fixed bottom-4 right-4 w-96 max-w-[calc(100vw-2rem)] bg-green-50 border-green-200 shadow-lg z-50">
      {isUpdating ? (
        <RefreshCw className="h-4 w-4 text-green-600 animate-spin" />
      ) : (
        <RefreshCw className="h-4 w-4 text-green-600" />
      )}

      <AlertDescription className="flex items-center justify-between">
        <div className="flex-1 pr-2">
          <p className="font-medium text-green-800">
            Aggionramento Disponibile
          </p>
          <p className="text-sm text-green-600">
            Una nuova version di CatchUp è disponibile!
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={updateAndReload}
            disabled={isUpdating}
            size="sm"
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            {isUpdating ? "Aggiornamento..." : "Aggiorna"}
          </Button>
          <Button
            onClick={dismissUpdate}
            variant="ghost"
            size="sm"
            className="text-green-600 hover:text-green-700 h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </AlertDescription>
    </Alert>
  );
};
